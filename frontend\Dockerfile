# Angular Uygulamasını Derleme Aşaması
FROM node:20-alpine AS angular-build
WORKDIR /app

COPY package.json package-lock.json ./
RUN npm install

COPY . .
RUN npm run build --configuration=production

# NGINX ile Yayın Aşaması
FROM nginx:stable-alpine AS angular-nginx

# Türkiye saat dilimi
RUN apk add --no-cache tzdata && \
    cp /usr/share/zoneinfo/Europe/Istanbul /etc/localtime && \
    echo "Europe/Istanbul" > /etc/timezone

# Build edilmiş Angular uygulamasını kopyala
COPY --from=angular-build /app/dist/btk-ng/browser /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf

EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]

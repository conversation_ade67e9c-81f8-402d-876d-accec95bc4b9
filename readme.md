
# Node.js ve Angular 19 Geliştirme Ortamı Kurulum Rehberi

Bu doküman, Node.js, Angular 19 ve PostgreSQL tabanlı bir projenin geliştirme ortamını kurmak ve çalıştırmak isteyen kullanıcılar için hazırlanmıştır. Re<PERSON>ber<PERSON>, gere<PERSON><PERSON> bağımlılıkların nasıl kurulacağı ve uygulamanın nasıl çalıştırılacağı adım adım açıklanmıştır.

---

## 🧩 Sistem Gereksinimleri

- **İşletim Sistemi**: Windows, macOS veya Linux
- **Terminal / Komut Satırı Aracı**
- **Docker** (veritabanı için)
- **nvm** (Node Version Manager – Node.js sürüm yönetimi için)

---

## 1. NVM (Node Version Manager) ile Node.js Kurulumu

### 1.1 NVM Kurulumu

#### macOS / Linux İçin:

Aşağıdaki komutu terminalde çalıştırarak `nvm` kurulumunu gerçekleştirebilirsiniz:

```bash
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.7/install.sh | bash
```

Kurulum sonrasında terminalinizi yeniden başlatmanız veya aşağıdaki komutlardan birini çalıştırmanız gerekebilir:

```bash
source ~/.bashrc
# veya
source ~/.zshrc
# veya
source ~/.profile
```

#### Windows İçin:

- [https://github.com/coreybutler/nvm-windows/releases](https://github.com/coreybutler/nvm-windows/releases) adresinden en güncel `nvm-setup.exe` dosyasını indirerek kurulumu gerçekleştirin.

### 1.2 Node.js Kurulumu

NVM aracılığıyla en güncel LTS (Long Term Support) sürümünü kurmak ve kullanmak için:

```bash
nvm install --lts
nvm use --lts
```

Kurulumu doğrulamak için:

```bash
node -v
npm -v
```

---

## 2. Angular CLI Kurulumu

Angular CLI 19 sürümünü küresel (global) olarak yüklemek için:

```bash
npm install -g @angular/cli@19
```

Kurulumu doğrulamak için:

```bash
ng version
```

> Çıktıda `Angular CLI: 19.X.YY` satırının yer aldığından emin olunuz.

---

## 3. PostgreSQL Kurulumu (Docker Kullanarak)

Proje, PostgreSQL veritabanı kullanmaktadır. Docker üzerinden aşağıdaki komut ile örnek bir veritabanı başlatılabilir:

```bash
docker run --name demo-postgres \
  -e POSTGRES_DB=demo_db \
  -e POSTGRES_USER=demo_user \
  -e POSTGRES_PASSWORD=demo_password \
  -p 5432:5432 \
  -d postgres:15
```

---

## 4. Backend (Sunucu Uygulaması) Çalıştırma

Backend tarafı Java ile geliştirilmiştir. Uygulamayı başlatmak için `Application.java` dosyası çalıştırılmalıdır.

Uygulamanın çalıştığını doğrulamak için tarayıcıda aşağıdaki URL'yi ziyaret edebilirsiniz:

```
http://localhost:8080/swagger-ui/index.html
```

---

## 5. Frontend (Angular Uygulaması) Çalıştırma

Aşağıdaki adımları izleyerek Angular uygulamasını başlatabilirsiniz:

```bash
cd frontend         # Frontend dizinine geçin
npm install         # Bağımlılıkları yükleyin
npm start           # Uygulamayı başlatın
```

Uygulama çalıştıktan sonra tarayıcıda şu adresi ziyaret edin:

```
http://localhost:4200
```

Giriş bilgileri:

- **Kullanıcı Adı**: `admin`
- **Parola**: `1`

---

## 6. Docker Compose ile Tüm Servisleri Başlatma

Projeyi tüm servisleriyle birlikte çalıştırmak için kök (root) dizine geçerek aşağıdaki komutu kullanabilirsiniz:
Öncesinde kurduğunuz db varsa o db'yi silmeniz gereklidir. Docker kendi networku içinde çalıştığı içini ilgili db'ye ulaşamayacaktır.
```bash
docker compose up -d
```

Bu komut backend, frontend ve veritabanı konteynerlerini arka planda başlatacaktır.

---

## 7. Kontrol Listesi

| Bileşen                    | Kontrol Etme Yolu                                      |
|---------------------------|--------------------------------------------------------|
| Angular uygulaması        | http://localhost:4200                                 |
| Backend API (Swagger UI)  | http://localhost:8080/swagger-ui/index.html           |
| PostgreSQL veritabanı     | Docker konteynerinin aktif olması                     |

---


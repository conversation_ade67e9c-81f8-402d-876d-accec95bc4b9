import { Injectable, signal,computed, effect  } from '@angular/core';

import { AuthService } from '../authentication/auth.service';
import { MenuItem } from 'primeng/api';
import { MenuItemModel } from './menu-item.model';
import { HttpClient } from '@angular/common/http';
import { environment } from '../../enviroments/environment';
import { MenuItemDto } from '../menu-yonetimi/menu-item.model';


@Injectable({
  providedIn: 'root'
})
export class MenuService {
  menuItems = signal<MenuItemModel[]>([]); 
  userRoles = signal<string[]>([]);

  constructor(private authService: AuthService,private http: HttpClient) {
    //this.userRoles.set(this.authService.getUserPermissions());
    //this.loadMenu();

    // AuthService içindeki authChanged event'ini dinle
    effect(() => {
      if (this.authService.authChanged()) {
        this.userRoles.set(this.authService.getUserPermissions());
        this.loadMenu();
      }
    });
  }
loadMenuAsync(): Promise<void> {
  return new Promise((resolve) => {
    this.http.get<MenuItemDto[]>(environment.apiUrl + "/menu-items/menuagacigetir").subscribe(response => {
      const mappedMenu: MenuItemModel[] = this.mapMenuItems(response);
      this.menuItems.set(mappedMenu);
      resolve();
    });
  });
}

  loadMenu() {
    this.http.get<MenuItemDto[]>(environment.apiUrl + "/menu-items/menuagacigetir").subscribe(response => {
      
      const mappedMenu: MenuItemModel[] = this.mapMenuItems(response);
  
      this.menuItems.set(mappedMenu); // Menü sistemine set ediyoruz
    });
  }
  
  // Yardımcı fonksiyon:
private mapMenuItems(menuItems: MenuItemDto[] | null | undefined): MenuItemModel[] {
  if (!menuItems) return [];

  return menuItems.map(menuItem => ({
    label: menuItem.label,
    icon: menuItem.icon,
    routerLink: menuItem.routerLink,
    roles: menuItem.yetkiAdlari || [], // ⬅️ string roller
    items: menuItem.items ? this.mapMenuItems(menuItem.items) : undefined
  }));
}


  clearMenu() {
    this.menuItems.set([]); // Çıkış yapıldığında menüyü sıfırla
  }

  hasPermission(menuRoles?: string[]): boolean {
    if (this.userRoles().includes('KullaniciTipi.Admin')) {
      return true; // Eğer kullanıcı "admin" ise her şeyi görebilir
    }

    if (!menuRoles || menuRoles.length === 0) {
      return true; // Eğer öğenin belirli bir rol gereksinimi yoksa herkese göster
    }
    let hasRole=menuRoles.some(role => this.userRoles().includes(role));
      return hasRole; // Kullanıcının herhangi bir rolü eşleşiyorsa göster
  }

private filterMenuItems(menuItems: MenuItemModel[]): MenuItemModel[] {
  return menuItems
    .map(item => ({
      ...item,
      items: item.items ? this.filterMenuItems(item.items.filter(sub => this.hasPermission(sub.roles))) : undefined
    }))
    .filter(item =>
      (item.items && item.items.length > 0) ||
      item.routerLink ||
      item.command
    );
}

getMenu = computed(() => {
  return this.filterMenuItems(this.menuItems());
});


}

version: '3.8'

services:
  postgres:
    image: postgres:15
    container_name: demo-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: demo_db
      POSTGRES_USER: demo_user
      POSTGRES_PASSWORD: demo_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data

  backend:
    build:
      context: .
      dockerfile: backend/Dockerfile
    container_name: btk-demo-app
    restart: unless-stopped
    ports:
      - "8080:8080"
    depends_on:
      - postgres
    environment:
      SPRING_DATASOURCE_URL: ***************************************
      SPRING_DATASOURCE_USERNAME: demo_user
      SPRING_DATASOURCE_PASSWORD: demo_password


  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: btk-demo-frontend
    restart: unless-stopped
    ports:
      - "4200:80" # Angular prod build NGINX üzerinden 80 portunu dinler
    depends_on:
      - backend

volumes:
  postgres_data:

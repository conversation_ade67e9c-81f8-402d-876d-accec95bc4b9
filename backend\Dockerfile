# Aşama 1: Maven build
FROM maven:3.9.6-eclipse-temurin-21-alpine AS build

# Ana dizin
WORKDIR /project

# Tüm kaynakları kopyala (parent + modules)
COPY . .

# Sadece backend mod<PERSON><PERSON><PERSON><PERSON><PERSON> derle
RUN mvn -pl backend -am clean package -DskipTests

# Aşama 2: Runtime için minimal imaj
FROM eclipse-temurin:21-jre-alpine

RUN apk add --no-cache tzdata && \
    cp /usr/share/zoneinfo/Europe/Istanbul /etc/localtime && \
    echo "Europe/Istanbul" > /etc/timezone

VOLUME /tmp

# Build sonucu oluşan JAR'ı al
COPY --from=build /project/backend/target/*.jar app.jar

EXPOSE 8080

ENTRYPOINT ["java", "-jar", "/app.jar"]

import { Component } from '@angular/core';
import { RouterModule } from '@angular/router';
import { MenuService } from './app/menu/menu.service';
import { AuthService } from './app/authentication/auth.service';

@Component({
    selector: 'app-root',
    standalone: true,
    imports: [RouterModule],
    template: `<router-outlet></router-outlet>`
})
export class AppComponent {

    constructor(private menuService: MenuService, private authService: AuthService) {
  if (this.authService.checkAuth()) {
    this.menuService.loadMenu();
  }
}

}

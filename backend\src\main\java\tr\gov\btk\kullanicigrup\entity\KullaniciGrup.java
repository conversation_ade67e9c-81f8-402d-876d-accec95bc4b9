package tr.gov.btk.kullanicigrup.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import tr.gov.btk.kullanicigrupyetki.entity.KullaniciGrupYetki;
import tr.gov.btk.shared.entity.BaseEntity;

import java.util.ArrayList;
import java.util.List;

@Entity
@Table(name = "kullanici_gruplar")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class KullaniciGrup extends BaseEntity {

    @Column(nullable = false)
    private String ad;

    @OneToMany(mappedBy = "kullaniciGrup", cascade = CascadeType.ALL, orphanRemoval = true)
    private List<KullaniciGrupYetki> kullaniciGrupYetkiler = new ArrayList<>();
}

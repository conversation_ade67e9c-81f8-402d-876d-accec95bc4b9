CREATE TABLE menu_item_yetkiler
(
    id           BIGINT GENERATED BY DEFAULT AS IDENTITY NOT NULL,
    is_deleted   BO<PERSON><PERSON><PERSON>,
    created_at   TIMESTAMP WITHOUT TIME ZONE,
    updated_at   TIMESTAMP WITHOUT TIME ZONE,
    deleted_at   TIM<PERSON><PERSON><PERSON> WITHOUT TIME ZONE,
    created_by   <PERSON><PERSON><PERSON><PERSON>(255),
    updated_by   <PERSON><PERSON><PERSON><PERSON>(255),
    deleted_by   <PERSON><PERSON><PERSON><PERSON>(255),
    menu_item_id BIGINT,
    yetki_id     BIGINT,
    CONSTRAINT pk_menu_item_yetkiler PRIMARY KEY (id)
);

CREATE TABLE menu_itemler
(
    id           BIGINT GENERATED BY DEFAULT AS IDENTITY NOT NULL,
    is_deleted   BO<PERSON>EA<PERSON>,
    created_at   TIMESTAMP WITHOUT TIME ZONE,
    updated_at   TIMESTAMP WITHOUT TIME ZONE,
    deleted_at   TIMESTAMP WITHOUT TIME ZONE,
    created_by   <PERSON><PERSON><PERSON><PERSON>(255),
    updated_by   <PERSON><PERSON><PERSON><PERSON>(255),
    deleted_by   <PERSON><PERSON><PERSON><PERSON>(255),
    label        <PERSON><PERSON><PERSON><PERSON>(255),
    icon         VARCHAR(255),
    router_link  VARCHAR(255),
    query_params VARCHAR(255),
    menu_order   INTEGER                                 NOT NULL,
    parent_id    BIGINT,
    CONSTRAINT pk_menu_itemler PRIMARY KEY (id)
);

ALTER TABLE menu_itemler
    ADD CONSTRAINT FK_MENU_ITEMLER_ON_PARENT FOREIGN KEY (parent_id) REFERENCES menu_itemler (id);

ALTER TABLE menu_item_yetkiler
    ADD CONSTRAINT FK_MENU_ITEM_YETKILER_ON_MENU_ITEM FOREIGN KEY (menu_item_id) REFERENCES menu_itemler (id);

ALTER TABLE menu_item_yetkiler
    ADD CONSTRAINT FK_MENU_ITEM_YETKILER_ON_YETKI FOREIGN KEY (yetki_id) REFERENCES yetkiler (id);
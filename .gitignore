# ---------------------------
# Java / Spring Boot bölümü
# ---------------------------
# Maven
target/
!.mvn/wrapper/maven-wrapper.jar
**/logs/
**/build/
*.log
*.class
*.jar
*.war
*.ear
hs_err_pid*
*.iml
*.ipr
*.iws

# Gradle (varsa)
.gradle/
build/
!gradle/wrapper/gradle-wrapper.jar

# IntelliJ IDEA
.idea/
*.iws
*.iml
*.ipr
out/

# Eclipse
.project
.classpath
.c9/
.settings/
.springBeans

# VS Code
.vscode/

# OS dosyaları
.DS_Store
Thumbs.db

# ---------------------------
# Ang<PERSON> bölümü (frontend)
# ---------------------------
/frontend/node_modules/
/frontend/dist/
/frontend/.angular/cache/
/frontend/.angular/
/frontend/.vscode/
**/*.tsbuildinfo

# npm log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Environment dosyaları
/frontend/src/environments/environment.*.ts

# IDE
/frontend/.idea/

# Test ve coverage çıktıları
/frontend/coverage/

# OS
frontend/.DS_Store

# ---------------------------
# Ortak .env
# ---------------------------
.env

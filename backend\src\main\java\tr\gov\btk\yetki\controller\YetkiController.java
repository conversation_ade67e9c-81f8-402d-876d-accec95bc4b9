package tr.gov.btk.yetki.controller;

import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import tr.gov.btk.yetki.dto.YetkiDto;
import tr.gov.btk.yetki.service.YetkiService;

import java.util.List;

@RestController
@RequestMapping("/api/yetkiler")
@RequiredArgsConstructor
public class YetkiController {

    private final YetkiService yetkiService;

    @GetMapping
    public ResponseEntity<List<YetkiDto>> getAll() {
        return ResponseEntity.ok(yetkiService.findAll());
    }

    @GetMapping("/ViewRolleriniGetir")
    public ResponseEntity<List<YetkiDto>> getViewRolleri() {
        return ResponseEntity.ok(yetkiService.getViewRolleri());
    }

    @GetMapping("/{id}")
    public ResponseEntity<YetkiDto> getById(@PathVariable Long id) {
        return ResponseEntity.ok(yetkiService.findById(id));
    }



    @PostMapping
    public ResponseEntity<YetkiDto> create(@RequestBody YetkiDto dto) {
        return ResponseEntity.ok(yetkiService.save(dto));
    }

    @PutMapping("/{id}")
    public ResponseEntity<YetkiDto> update(@PathVariable Long id, @RequestBody YetkiDto dto) {
        dto.setId(id);
        return ResponseEntity.ok(yetkiService.save(dto));
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<Void> delete(@PathVariable Long id) {
        yetkiService.delete(id);
        return ResponseEntity.noContent().build();
    }
}
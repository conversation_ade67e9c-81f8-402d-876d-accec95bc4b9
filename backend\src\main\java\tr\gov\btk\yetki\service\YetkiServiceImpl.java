package tr.gov.btk.yetki.service;

import org.springframework.stereotype.Service;
import tr.gov.btk.shared.service.BaseServiceImpl;
import tr.gov.btk.yetki.dto.YetkiDto;
import tr.gov.btk.yetki.entity.Yetki;
import tr.gov.btk.yetki.mapper.YetkiMapper;
import tr.gov.btk.yetki.repository.YetkiRepository;

import java.util.List;

@Service
public class YetkiServiceImpl extends BaseServiceImpl<Yetki, YetkiDto, Long> implements YetkiService {

    public YetkiServiceImpl(YetkiRepository repository, YetkiMapper mapper) {
        super(repository, mapper);
    }

    @Override
    public List<YetkiDto> getViewRolleri() {
        return mapper.toDtoList(
                repository.findAll().stream()
                        .filter(yetki -> yetki.getAd() != null && yetki.getAd().endsWith("_VIEW"))
                        .toList()
        );
    }
}
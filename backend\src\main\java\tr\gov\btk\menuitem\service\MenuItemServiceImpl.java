package tr.gov.btk.menuitem.service;

import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import tr.gov.btk.menuitem.dto.MenuItemDto;
import tr.gov.btk.menuitem.entity.MenuItem;
import tr.gov.btk.menuitem.mapper.MenuItemMapper;
import tr.gov.btk.menuitem.repository.MenuItemRepository;
import tr.gov.btk.menuitemyetki.entity.MenuItemYetki;
import tr.gov.btk.shared.service.BaseServiceImpl;
import tr.gov.btk.yetki.entity.Yetki;
import tr.gov.btk.yetki.repository.YetkiRepository;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;

@Service
public class MenuItemServiceImpl extends BaseServiceImpl<MenuItem, MenuItemDto, Long> implements MenuItemService {

    private final MenuItemRepository repository;
    private final MenuItemMapper mapper;
    private final YetkiRepository yetkiRepository;

    public MenuItemServiceImpl(MenuItemRepository repository, @Qualifier("menuItemMapperImpl") MenuItemMapper mapper, YetkiRepository yetkiRepository) {
        super(repository, mapper);
        this.repository = repository;
        this.mapper = mapper;
        this.yetkiRepository = yetkiRepository;
    }

    @Override
    public MenuItemDto save(MenuItemDto dto) {
        MenuItem entity;

        if (dto.getId() != null) {
            // Güncelleme
            entity = repository.findById(dto.getId())
                    .orElseThrow(() -> new RuntimeException("MenuItem bulunamadı: " + dto.getId()));
            mapper.updateEntityFromDto(dto, entity);
        } else {
            // Yeni kayıt
            entity = mapper.toEntity(dto);
        }

        if(dto.getParentId()!=null)
        {
            MenuItem parent=repository.findById(dto.getParentId()).orElseThrow(() -> new RuntimeException("Parent MenuItem bulunamadı: " + dto.getParentId()));
            entity.setParent(parent);
        }

        // Yetkileri set et (manuel):
        if (dto.getYetkiIds() != null&&dto.getYetkiIds().size() > 0) {
            // Öncekileri temizle (orphanRemoval çalışsın diye)
            entity.getMenuItemYetkiler().clear();

            List<MenuItemYetki> yeniYetkiler = dto.getYetkiIds().stream()
                    .map(yetkiId -> {
                        Yetki yetki = yetkiRepository.findById(yetkiId)
                                .orElseThrow(() -> new RuntimeException("Yetki bulunamadı: " + yetkiId));
                        return new MenuItemYetki(entity, yetki);
                    })
                    .toList();

            entity.getMenuItemYetkiler().addAll(yeniYetkiler);
        }

        return mapper.toDto(repository.save(entity));
    }

    public List<MenuItemDto> menuAgaciGetir() {
        List<MenuItem> menuItemler = repository.findAllWithYetkilerAndParent();

        List<MenuItemDto> result = new ArrayList<>();

        List<MenuItem> kokler = menuItemler.stream()
                .filter(item -> item.getParent() == null)
                .sorted(Comparator.comparing(MenuItem::getMenuOrder))
                .toList();

        for (MenuItem kok : kokler) {
            MenuItemDto kokDto = mapper.toDto(kok);

            List<MenuItemDto> yaprakDtos = menuItemler.stream()
                    .filter(item -> item.getParent() != null && item.getParent().getId().equals(kok.getId()))
                    .sorted(Comparator.comparing(MenuItem::getMenuOrder))
                    .map(item -> mapper.toDto(item))
                    .toList();

            if (!yaprakDtos.isEmpty()) {
                kokDto.setItems(yaprakDtos);
            }

            result.add(kokDto);
        }

        return result;
    }

    @Override
    public MenuItemDto update(Long id, MenuItemDto dto) {
        dto.setId(id);
        return save(dto);
    }

}


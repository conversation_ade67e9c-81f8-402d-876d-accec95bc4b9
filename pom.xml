<?xml version="1.0" encoding="UTF-8"?>

<project xmlns="http://maven.apache.org/POM/4.0.0"

         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"

         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">

    <modelVersion>4.0.0</modelVersion>

 

    <groupId>angular-demo</groupId>

    <artifactId>angular-demo</artifactId>

    <version>1.0</version>

   <packaging>pom</packaging>

 

    <parent>

        <groupId>org.springframework.boot</groupId>

        <artifactId>spring-boot-starter-parent</artifactId>

        <version>3.2.7</version>

        <relativePath/> <!-- lookup parent from repository -->

    </parent>

 

    <properties>

        <java.version>17</java.version>

        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>

        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>

    </properties>

 

    <dependencies>

        <!-- Web -->

        <dependency>

            <groupId>org.springframework.boot</groupId>

            <artifactId>spring-boot-starter-web</artifactId>

        </dependency>

 

        <dependency>

            <groupId>org.springdoc</groupId>

            <artifactId>springdoc-openapi-starter-webmvc-ui</artifactId>

            <version>2.5.0</version>

        </dependency>

 

        <dependency>

            <groupId>io.jsonwebtoken</groupId>

            <artifactId>jjwt-api</artifactId>

            <version>0.11.5</version>

        </dependency>

        <dependency>

            <groupId>io.jsonwebtoken</groupId>

            <artifactId>jjwt-impl</artifactId>

            <version>0.11.5</version>

            <scope>runtime</scope>

        </dependency>

        <dependency>

            <groupId>io.jsonwebtoken</groupId>

            <artifactId>jjwt-jackson</artifactId>

            <version>0.11.5</version>

            <scope>runtime</scope>

        </dependency>

 

 

        <!-- Spring Data JPA -->

        <dependency>

            <groupId>org.springframework.boot</groupId>

            <artifactId>spring-boot-starter-data-jpa</artifactId>

        </dependency>

 

        <!-- PostgreSQL Driver -->

        <dependency>

            <groupId>org.postgresql</groupId>

            <artifactId>postgresql</artifactId>

            <version>42.7.3</version>

        </dependency>

 

        <!-- Flyway (DB migration) -->

        <dependency>

            <groupId>org.flywaydb</groupId>

            <artifactId>flyway-core</artifactId>

 

        </dependency>

 

        <!-- Lombok -->

        <dependency>

            <groupId>org.projectlombok</groupId>

            <artifactId>lombok</artifactId>

            <version>1.18.36</version>

            <scope>provided</scope>

        </dependency>

 

        <!-- MapStruct (DTO Mapping) -->

        <dependency>

            <groupId>org.mapstruct</groupId>

            <artifactId>mapstruct</artifactId>

            <version>1.5.5.Final</version>

        </dependency>

        <dependency>

            <groupId>org.mapstruct</groupId>

            <artifactId>mapstruct-processor</artifactId>

            <version>1.5.5.Final</version>

            <scope>provided</scope>

        </dependency>

 

        <!-- Bean Validation -->

        <dependency>

            <groupId>jakarta.validation</groupId>

            <artifactId>jakarta.validation-api</artifactId>

            <version>3.0.2</version>

        </dependency>

        <dependency>

            <groupId>org.hibernate.validator</groupId>

            <artifactId>hibernate-validator</artifactId>

        </dependency>

 

        <!-- Spring Security -->

        <dependency>

            <groupId>org.springframework.boot</groupId>

            <artifactId>spring-boot-starter-security</artifactId>

        </dependency>

 

        <!-- Test -->

        <dependency>

            <groupId>org.springframework.boot</groupId>

            <artifactId>spring-boot-starter-test</artifactId>

            <scope>test</scope>

        </dependency>

    </dependencies>

 

    <modules>

        <module>backend</module>

    </modules>

 

 

    <build>

        <plugins>

            <!-- Spring Boot Plugin -->

            <plugin>

                <groupId>org.springframework.boot</groupId>

                <artifactId>spring-boot-maven-plugin</artifactId>

            </plugin>

 

            <!-- Java Compiler -->

            <plugin>

                <groupId>org.apache.maven.plugins</groupId>

                <artifactId>maven-compiler-plugin</artifactId>

                <version>3.11.0</version>

                <configuration>

                    <source>${java.version}</source>

                    <target>${java.version}</target>

                    <encoding>${project.build.sourceEncoding}</encoding>

                    <compilerArgs>

                        <arg>-parameters</arg>

                    </compilerArgs>

                    <annotationProcessorPaths>

                        <path>

                            <groupId>org.projectlombok</groupId>

                            <artifactId>lombok</artifactId>

                            <version>1.18.36</version>

                        </path>

                        <path>

                            <groupId>org.mapstruct</groupId>

                            <artifactId>mapstruct-processor</artifactId>

                            <version>1.5.5.Final</version>

                        </path>

                    </annotationProcessorPaths>

                </configuration>

            </plugin>

        </plugins>

    </build>

</project>
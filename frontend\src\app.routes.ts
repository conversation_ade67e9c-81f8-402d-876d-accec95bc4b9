import { Routes } from '@angular/router';
import { AppLayout } from './app/layout/component/app.layout';

import { LoginComponent } from './app/authentication/login.component';
import { AuthGuard } from './app/authentication/auth.guard';
import { NotFoundComponent } from './app/not-found/not-found.component';
import { MenuYonetimiComponent } from './app/menu-yonetimi/menu-yonetimi.component';
import { KullaniciYonetimiComponent } from './app/kullanici-yonetimi/kullanici-yonetimi.component';
import { KullaniciGrupYonetimiComponent } from './app/kullanici-grup-yonetimi/kullanici-grup-yonetimi.component';
import { RolYonetimiComponent } from './app/rol-yonetimi/rol-yonetimi.component';
import { ResetPasswordComponent } from './app/reset-password/reset-password.component';
import { UlkeYonetimiComponent } from './app/ulke-yonetimi/ulke-yonetimi.component';
import { TestYonetimiComponent } from './app/test-yonetimi/test-yonetimi.component';

export const appRoutes: Routes = [
    {
        path: '', 
        component: AppLayout,
        children: [
            { path: 'not-found', component: NotFoundComponent },
            { path: 'menuler', component: MenuYonetimiComponent, canActivate: [AuthGuard] },
            { path: 'kullanicilar', component: KullaniciYonetimiComponent, canActivate: [AuthGuard] },
            { path: 'kullanici-gruplar', component: KullaniciGrupYonetimiComponent, canActivate: [AuthGuard] },
            { path: 'yetkiler', component: RolYonetimiComponent, canActivate: [AuthGuard] }, 
            { path: 'ulkeler', component: UlkeYonetimiComponent, canActivate: [AuthGuard] },
             { path: 'testler', component: TestYonetimiComponent, canActivate: [AuthGuard] }

           
        ], canActivate: [AuthGuard] 
    },
    { path: 'login', component: LoginComponent },
    { path: 'reset-password', component: ResetPasswordComponent },
    { path: '**', redirectTo: '/not-found' } // Burada 404 sayfasına yönlendiriyoruz
    
];

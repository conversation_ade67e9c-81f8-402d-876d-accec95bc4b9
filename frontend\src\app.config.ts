import { HTTP_INTERCEPTORS, HttpHeaders, provideHttpClient, withFetch, withInterceptors } from '@angular/common/http';
import { ApplicationConfig } from '@angular/core';
import { provideAnimationsAsync } from '@angular/platform-browser/animations/async';
import { provideRouter, withEnabledBlockingInitialNavigation, withInMemoryScrolling } from '@angular/router';
import Aura from '@primeng/themes/aura';
import { providePrimeNG } from 'primeng/config';
import { appRoutes } from './app.routes';
import { AuthInterceptor } from './app/authentication/authInterceptor.interceptor';

export const appConfig: ApplicationConfig = {
    providers: [
        provideRouter(appRoutes, withInMemoryScrolling({ anchorScrolling: 'enabled', scrollPositionRestoration: 'enabled' }), withEnabledBlockingInitialNavigation()),
        provideHttpClient(
            withFetch(), // fetch ile çalışmak istiyoruz
            withInterceptors([
                (request, next) => {
                  const loginUrl = '/ui/Auth/login'; // senin login API endpointin

                  if (request.url.includes(loginUrl)) {
                    // Eğer login isteğiyse hiçbir değişiklik yapmadan devam
                    return next(request);
                  }
                  let token = localStorage.getItem("token");
                  if(token===null)
                  {
                    token= localStorage.getItem("extoken");
                  }
                  const headers = new HttpHeaders({
                    Authorization: `Bearer ${token}`
                  });
              
                  const updatedRequest = request.clone({
                    headers
                  });
              
                  return next(updatedRequest);
                }
              ])
          ),
        provideAnimationsAsync(),
        providePrimeNG({ theme: { preset: Aura, options: { darkModeSelector: '.app-dark' } } }),
        { provide: HTTP_INTERCEPTORS, useClass: AuthInterceptor, multi: true }
    ]
};
